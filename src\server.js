const http = require('http');
const responseHandler = require('./responses.js');
const JSONResponseHandler = require('./jsonResponses.js');
const XMLResponseHandler = require('./xmlResponses.js');

const port = process.env.PORT || process.env.NODE_PORT || 3000;

const urlStruct = {
    '/': responseHandler.getIndex,
    '/success': JSONResponseHandler.success,
    '/badRequest': JSONResponseHandler.badRequest,
    '/unauthorized': JSONResponseHandler.unauthorized,
    '/forbidden': JSONResponseHandler.forbidden,
    '/internal': JSONResponseHandler.internal,
    '/notImplemented': JSONResponseHandler.notImplemented,
    '/style.css': responseHandler.getCSS,
};


const onRequest = (request, response) => {
    const protocol = request.connection.encrypted ? 'https' : 'http';
    const parsedUrl = new URL(request.url, `${protocol}://${request.headers.host}`);
    const params = {
        query: Object.fromEntries(parsedUrl.searchParams)
    }

    const acceptHeader = request.headers.accept || 'application/json';
    const useXML = acceptHeader.includes('text/xml');

    if (urlStruct[parsedUrl.pathname]) {
        if (useXML && XMLResponseHandler[parsedUrl.pathname.slice(1)]) {
            XMLResponseHandler[parsedUrl.pathname.slice(1)](request, response, params);
        } else {
            urlStruct[parsedUrl.pathname](request, response, params);
        }
    } else {
        if (useXML) {
            XMLResponseHandler.anythingElse(request, response, params);
        } else {
            JSONResponseHandler.anythingElse(request, response, params);
        }
    }
};

http.createServer(onRequest).listen(port);
console.log(`Listening on 127.0.0.1: ${port}`)